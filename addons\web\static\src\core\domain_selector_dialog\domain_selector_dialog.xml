<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="web.DomainSelectorDialog">
        <Dialog title="dialogTitle">
            <div t-if="props.text" class="mb-3" t-out="props.text"/>
            <DomainSelector t-props="domainSelectorProps" />
            <t t-set-slot="footer">
                <t t-if="props.readonly">
                    <button class="btn btn-secondary" t-on-click="() => props.close()">Close</button>
                </t>
                <t t-else="">
                    <button class="btn btn-primary" t-att-disabled="disabled" t-on-click="onConfirm" t-ref="confirm"><t t-esc="confirmButtonText"/></button>
                    <button class="btn btn-secondary" t-on-click="onDiscard"><t t-esc="discardButtonText"/></button>
                </t>
            </t>
        </Dialog>
    </t>

</templates>
