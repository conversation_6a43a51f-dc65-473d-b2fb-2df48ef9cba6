<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="web.Notebook">
        <div t-attf-class="o_notebook d-flex w-100 {{ props.orientation === 'horizontal' ? 'horizontal flex-column' : 'vertical flex-row' }} {{ props.className }}" t-if="state.currentPage">
            <div class="o_notebook_headers" t-att-class="{ 'm-0': props.orientation === 'vertical' }">
                <ul t-attf-class="nav nav-tabs {{ props.orientation === 'horizontal' ? 'flex-row flex-nowrap' : 'flex-column p-0' }}">
                    <li t-foreach="navItems" t-as="navItem" t-key="navItem[0]" class="nav-item flex-nowrap cursor-pointer" t-if="navItem[1].isVisible" t-attf-class="{{ navItem[1].isDisabled ? 'disabled' : '' }}">
                        <a class="nav-link" t-attf-class="{{ navItem[0] === state.currentPage ? 'active' : '' }} {{ props.orientation === 'vertical' ? 'p-3 rounded-0' : '' }} {{ navItem[1].className || '' }}" t-att-name="navItem[1].name" t-on-click.prevent="() => this.activatePage(navItem[0])" href="#" role="tab" tabindex="0">
                            <i t-if="props.icons and props.icons[navItem[0]]" t-attf-class="fa {{ props.icons[navItem[0]] }} me-2" />
                            <t t-esc="navItem[1].title" />
                        </a>
                    </li>
                </ul>
            </div>
            <div class="o_notebook_content tab-content">
                <div class="tab-pane active fade" t-ref="activePane">
                    <t t-if="page" t-component="page.Component" t-key="state.currentPage" t-props="page.props" />
                    <t t-else="" t-slot="{{ state.currentPage }}" />
                </div>
            </div>
        </div>
    </t>

</templates>
