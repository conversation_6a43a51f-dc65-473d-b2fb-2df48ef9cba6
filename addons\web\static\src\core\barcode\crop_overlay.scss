.o_crop_container {
    position: relative;

    > * {
        grid-row: 1 / -1;
        grid-column: 1 / -1;
    }

    .o_crop_overlay {
        background-color: RGB(0 0 0 / 0.75);
        mix-blend-mode: darken;

        &::after {
            content: '';
            display: block;
            height: 100%;
            width: 100%;
            clip-path: inset(var(--o-crop-y, 0px) var(--o-crop-x, 0px));
            background-color: white;
        }
    }

    .o_crop_icon {
        --o-crop-icon-width: 20px;
        --o-crop-icon-height: 20px;
        position: absolute;
        width: var(--o-crop-icon-width);
        height: var(--o-crop-icon-height);
        left: calc(var(--o-crop-icon-x, 0px) - (var(--o-crop-icon-width) / 2));
        top: calc(var(--o-crop-icon-y, 0px) - (var(--o-crop-icon-height) / 2));
    }
}
