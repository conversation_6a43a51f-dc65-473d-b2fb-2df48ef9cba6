<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.OverlayContainer">
        <div class="o-overlay-container" t-ref="root">
            <t t-foreach="sortedOverlays" t-as="overlay" t-key="overlay.id">
                <ErrorHandler t-if="isVisible(overlay)" onError="(error) => this.handleError(overlay, error)">
                    <OverlayItem env="overlay.env" component="overlay.component" props="overlay.props"/>
                </ErrorHandler>
            </t>
        </div>
    </t>

    <t t-name="web.OverlayContainer.Item">
        <div t-ref="rootRef" class="o-overlay-item">
            <t t-component="props.component" t-props="props.props" />
        </div>
    </t>

</templates>
