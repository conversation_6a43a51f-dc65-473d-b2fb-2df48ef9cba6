.o_select_menu {
    .o_select_menu_toggler {
        display: grid;
        grid-template-columns: auto 25px;
    }
    .o_select_menu_toggler.o_can_deselect {
        grid-template-columns: auto 25px 25px;
    }

    .o_select_menu_toggler_slot {
        flex-grow: 2;
    }

    .o_select_menu_toggler_caret {
        grid-column: 2;
    }
    .o_can_deselect .o_select_menu_toggler_caret {
        grid-column: 3;
    }

    .o_select_menu_toggler_clear {
        grid-column: 2;
    }

    .o_select_menu_toggler_clear:hover i {
        color: red;
    }

    .o_tag {
        margin: 2px;
    }

    &--sticky {
        position: sticky;
    }
}

.o_select_menu_menu {
    min-width: fit-content;
    max-height: 350px !important;

    input {
        cursor: text !important;
    }
    .o_select_menu_sticky {
        background-color: $dropdown-bg !important;

        &.o_select_menu_item.focus {
            background: $dropdown-border-color !important;
        }
    }
    .o_select_menu_group {
        top: 40px !important;
        &:not(.o_select_menu_searchable_group) {
            top: -4px;
        }
    }
    .o_select_active {
        color: white;
    }
    &.o_select_menu_multi_select {
        .o_select_active:hover {
            background: $o-danger !important;
            transition: background .5s;
        }
    }
}

.dropup .o_select_menu_menu {
    box-shadow: 0 -7px 10px rgba(8, 8, 8, 0.319);
}

.dropdown .o_select_menu_menu {
    box-shadow: 0 7px 10px rgba(8, 8, 8, 0.319);
}
