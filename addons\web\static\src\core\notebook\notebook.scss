.o_notebook {
    --notebook-margin-x: 0;
    --notebook-padding-x: 0;
    --notebook-link-border-color: transparent;
    --notebook-link-border-color-active: #{$border-color};
    --notebook-link-border-color-hover: #{$gray-200};
    --notebook-link-border-color-active-accent: #{$border-color};

    .o_notebook_headers {
        margin: 0 var(--notebook-margin-x, 0);
        overflow-x: auto;

        @include media-breakpoint-down(md) {
            &::-webkit-scrollbar {
                display: none;
            }
        }
    }

    .nav {
        padding: 0 var(--notebook-padding-x, 0);
        background-color: $o-view-background-color;
    }

    .nav-item {
        white-space: nowrap;
        margin: 0 -1px 0 0;

        &.disabled {
            .nav-link {
                cursor: not-allowed;
                opacity: .3;
            }
        }
    }

    .nav-link {
        border-color: var(--notebook-link-border-color, transparent);

        &.active {
            &, &:hover, &:focus, &:active {
                border-color: var(--notebook-link-border-color-active);
                border-top-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active));
                border-bottom-color: $o-view-background-color;
            }
        }

        &:hover, &:focus, &:active {
            outline: none;
        }

        &:hover {
            border-color: var(--notebook-link-border-color-hover);
        }
    }

    .tab-pane:not(.show) {
        transition: none;
    }

    &.vertical {
        .o_notebook_headers {
            overflow-x: visible;
        }

        .nav {
            width: max-content;
            border-bottom-color: transparent; // removing the width has weird side effect due to the negative margin
        }

        .nav-item {
            margin: 0 0 -1px 0;

            &:first-child .nav-link {
                border-top-width: 0;
            }
        }

        .nav-link {
            margin-bottom: 0;

            &.active {
                &, &:hover, &:focus, &:active {
                    border-color: var(--notebook-link-border-color-active);
                    border-left-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active));
                    border-right-color: $o-view-background-color;
                }
            }
        }
    }

    @include media-breakpoint-down(lg) {
        .o_notebook_content .oe-toolbar {
            @include o-position-sticky($top: 0px);
            margin-left: var(--notebook-margin-x, $o-horizontal-padding);
            margin-right: var(--notebook-margin-x, $o-horizontal-padding);
            width: auto;
        }
    }
}
