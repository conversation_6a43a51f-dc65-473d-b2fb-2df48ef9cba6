<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="web.ActionSwiper">
        <t t-if="props.onRightSwipe || props.onLeftSwipe">
            <div class="o_actionswiper" t-on-touchend="_onTouchEndSwipe" t-on-touchmove="_onTouchMoveSwipe" t-on-touchstart="_onTouchStartSwipe" t-ref="root">
                <div class="o_actionswiper_overflow_container position-relative overflow-hidden">
                    <div class="o_actionswiper_target_container" t-ref="targetContainer" t-att-style="state.containerStyle" t-att-class="{ o_actionswiper_swiping: state.isSwiping }">
                        <t t-slot="default"/>
                        <t t-if="localizedProps.onRightSwipe and (localizedProps.onRightSwipe.icon or localizedProps.onRightSwipe.bgColor)">
                            <div t-att-style="'max-width: ' + swipedDistance + 'px;'" class="o_actionswiper_right_swipe_area position-absolute overflow-hidden w-100 h-100 d-flex align-items-center justify-content-center rounded-end" t-att-class="localizedProps.onRightSwipe.bgColor">
                                <span><i class="fa fa-2x" t-att-class="localizedProps.onRightSwipe.icon"/></span>
                            </div>
                        </t>
                        <t t-if="localizedProps.onLeftSwipe and (localizedProps.onLeftSwipe.icon or localizedProps.onLeftSwipe.bgColor)">
                            <div t-att-style="'max-width: ' + -swipedDistance + 'px;'" class="o_actionswiper_left_swipe_area position-absolute overflow-hidden w-100 h-100 d-flex align-items-center justify-content-center rounded-start" t-att-class="localizedProps.onLeftSwipe.bgColor">
                                <span><i class="fa fa-2x" t-att-class="localizedProps.onLeftSwipe.icon"/></span>
                            </div>
                        </t>
                    </div>
                </div>
            </div>
        </t>
        <t t-else="">
            <t t-slot="default"/>
        </t>
    </t>
</templates>
