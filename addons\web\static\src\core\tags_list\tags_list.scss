.o_tag {
    font-size: var(--Tag-font-size, #{$font-size-sm});
    max-width: var(--Tag-max-width, 100%);

    @for $size from 1 through length($o-colors) {
        &.o_tag_color_#{$size - 1} {
            $-bg: adjust-color(nth($o-colors, $size), $lightness: 25%, $saturation: 15%);
            $-color: adjust-color(nth($o-colors, $size), $lightness: -40%, $saturation: -15%);

            &, &::after {
                @include o-print-color($-bg, background-color, bg-opacity);
                @include o-print-color($-color, color, text-opacity);
            }
        }
    }
}