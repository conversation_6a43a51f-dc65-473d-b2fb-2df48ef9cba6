<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.AutoComplete">
        <div class="o-autocomplete" t-ref="root" t-att-class="autoCompleteRootClass">
            <input
                type="text"
                t-att-id="props.id"
                class="o-autocomplete--input o_input pe-3"
                autocomplete="off"
                t-att-placeholder="props.placeholder"
                role="combobox"
                t-att-aria-activedescendant="activeSourceOptionId"
                t-att-aria-expanded="displayOptions ? 'true' : 'false'"
                aria-autocomplete="list"
                aria-haspopup="listbox"
                t-model="state.value"
                t-on-blur="onInputBlur"
                t-on-click="onInputClick"
                t-on-change="onInputChange"
                t-on-input="onInput"
                t-on-keydown="onInputKeydown"
                t-on-focus="onInputFocus"
                t-ref="input"
            />
            <t t-if="displayOptions">
                <ul
                    role="menu"
                    class="o-autocomplete--dropdown-menu ui-widget show"
                    t-att-class="ulDropdownClass"
                    t-ref="sourcesList">
                    <t t-foreach="sources" t-as="source" t-key="source.id">
                        <t t-if="source.isLoading">
                            <li class="ui-menu-item"
                                t-att-class="{
                                    'o-autocomplete--dropdown-item': props.dropdown,
                                    'd-block': !props.dropdown
                                }">
                                <a
                                    t-attf-id="{{props.id or 'autocomplete'}}_{{source_index}}_loading"
                                    role="option"
                                    href="#"
                                    class="o_loading dropdown-item ui-menu-item-wrapper"
                                    aria-selected="true"
                                >
                                    <i class="fa fa-spin fa-circle-o-notch" /> <t t-esc="source.placeholder" />
                                </a>
                            </li>
                        </t>
                        <t t-else="">
                            <t t-foreach="source.options" t-as="option" t-key="option.id">
                                <li
                                    class="o-autocomplete--dropdown-item ui-menu-item d-block"
                                    t-att-class="option.classList"
                                    t-on-mouseenter="() => this.onOptionMouseEnter([source_index, option_index])"
                                    t-on-mouseleave="() => this.onOptionMouseLeave([source_index, option_index])"
                                    t-on-click="() => this.onOptionClick(option)"
                                    t-on-pointerdown="() => this.ignoreBlur = true"
                                >
                                    <a
                                        t-attf-id="{{props.id or 'autocomplete'}}_{{source_index}}_{{option_index}}"
                                        role="option"
                                        href="#"
                                        class="dropdown-item ui-menu-item-wrapper text-truncate"
                                        t-att-class="{ 'ui-state-active': isActiveSourceOption([source_index, option_index]) }"
                                        t-att-aria-selected="isActiveSourceOption([source_index, option_index]) ? 'true' : 'false'"
                                    >
                                        <t t-if="source.optionTemplate">
                                            <t t-call="{{ source.optionTemplate }}" />
                                        </t>
                                        <t t-else="">
                                            <t t-esc="option.label" />
                                        </t>
                                    </a>
                                </li>
                            </t>
                        </t>
                    </t>
                </ul>
            </t>
        </div>
    </t>

</templates>
