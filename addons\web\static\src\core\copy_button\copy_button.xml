<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="web.CopyButton">
        <button
            class="text-nowrap"
            t-ref="button"
            t-att-disabled="props.disabled"
            t-attf-class="btn o_clipboard_button {{ props.className || '' }}"
            t-on-click.stop="onClick"
        >
            <span class="mx-1" t-attf-class="fa {{ props.icon || 'fa-clipboard' }}"/>
            <span t-if="props.copyText" t-esc="props.copyText"/>
        </button>
    </t>

</templates>
